#!/usr/bin/env python3
"""
Demonstration of relative movement methods.

This script shows how to use movel_relative and movej_relative
for precise robot control.
"""

import math
from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints


def demo_relative_movements():
    """Demonstrate relative movement capabilities."""
    print("=== URRobot Relative Movement Demo ===")
    
    robot = URRobot()
    
    # Show current state
    print("\n1. Reading current robot state...")
    current_joints = robot.get_joints()
    current_pose = robot.get_pose()
    
    print(f"Current joint positions (degrees):")
    print(f"  J1: {current_joints.j1*180/math.pi:6.1f}°")
    print(f"  J2: {current_joints.j2*180/math.pi:6.1f}°")
    print(f"  J3: {current_joints.j3*180/math.pi:6.1f}°")
    print(f"  J4: {current_joints.j4*180/math.pi:6.1f}°")
    print(f"  J5: {current_joints.j5*180/math.pi:6.1f}°")
    print(f"  J6: {current_joints.j6*180/math.pi:6.1f}°")
    
    print(f"\nCurrent TCP pose:")
    print(f"  Position: X={current_pose.x:6.3f}m, Y={current_pose.y:6.3f}m, Z={current_pose.z:6.3f}m")
    print(f"  Rotation: Rx={current_pose.rx*180/math.pi:6.1f}°, Ry={current_pose.ry*180/math.pi:6.1f}°, Rz={current_pose.rz*180/math.pi:6.1f}°")
    
    # Demonstrate relative joint movements
    print("\n2. Relative Joint Movement Examples:")
    print("   (All movements are commented out for safety)")
    
    # Example 1: Single joint adjustment
    print("\n   Example 1: Move joint 1 by +5 degrees")
    j1_delta = Joints(j1=math.radians(5), j2=0, j3=0, j4=0, j5=0, j6=0)
    print(f"   Delta: J1={j1_delta.j1*180/math.pi:.1f}° (others=0°)")
    print(f"   Command: robot.movej_relative({j1_delta})")
    # robot.movej_relative(j1_delta)  # Uncomment to execute
    
    # Example 2: Multiple joint adjustment
    print("\n   Example 2: Adjust multiple joints")
    multi_joint_delta = Joints(
        j1=math.radians(2),   # +2°
        j2=math.radians(-3),  # -3°
        j3=0,
        j4=math.radians(5),   # +5°
        j5=0,
        j6=math.radians(-1)   # -1°
    )
    print(f"   Delta: J1={multi_joint_delta.j1*180/math.pi:.1f}°, J2={multi_joint_delta.j2*180/math.pi:.1f}°, J4={multi_joint_delta.j4*180/math.pi:.1f}°, J6={multi_joint_delta.j6*180/math.pi:.1f}°")
    print(f"   Command: robot.movej_relative({multi_joint_delta})")
    # robot.movej_relative(multi_joint_delta)  # Uncomment to execute
    
    # Demonstrate relative cartesian movements
    print("\n3. Relative Cartesian Movement Examples:")
    
    # Example 3: Pure translation
    print("\n   Example 3: Move 2cm forward, 1cm up")
    translation_delta = Pose3D(x=0.02, y=0.0, z=0.01, rx=0, ry=0, rz=0)
    print(f"   Delta: X={translation_delta.x:.3f}m, Z={translation_delta.z:.3f}m")
    print(f"   Command: robot.movel_relative({translation_delta})")
    # robot.movel_relative(translation_delta)  # Uncomment to execute
    
    # Example 4: Pure rotation
    print("\n   Example 4: Rotate tool 10° around Z-axis")
    rotation_delta = Pose3D(x=0, y=0, z=0, rx=0, ry=0, rz=math.radians(10))
    print(f"   Delta: Rz={rotation_delta.rz*180/math.pi:.1f}°")
    print(f"   Command: robot.movel_relative({rotation_delta})")
    # robot.movel_relative(rotation_delta)  # Uncomment to execute
    
    # Example 5: Combined movement
    print("\n   Example 5: Combined translation and rotation")
    combined_delta = Pose3D(
        x=0.01, y=-0.005, z=0.02,        # 1cm forward, 0.5cm left, 2cm up
        rx=0, ry=0, rz=math.radians(-5)  # rotate -5° around Z
    )
    print(f"   Delta: X={combined_delta.x:.3f}m, Y={combined_delta.y:.3f}m, Z={combined_delta.z:.3f}m, Rz={combined_delta.rz*180/math.pi:.1f}°")
    print(f"   Command: robot.movel_relative({combined_delta})")
    # robot.movel_relative(combined_delta)  # Uncomment to execute
    
    # Show practical use cases
    print("\n4. Practical Use Cases:")
    print("""
   Calibration sequence:
   # Fine-tune position during calibration
   for step in range(5):
       # Take measurement
       measurement = take_measurement()
       
       # Calculate correction
       if measurement.error_x > threshold:
           correction = Pose3D(x=0.001, y=0, z=0, rx=0, ry=0, rz=0)  # 1mm step
           robot.movel_relative(correction)
   
   Teaching sequence:
   # Teach positions incrementally
   robot.movej_relative(Joints(j1=math.radians(5), j2=0, j3=0, j4=0, j5=0, j6=0))
   input("Press Enter when position looks good...")
   robot.movej_relative(Joints(j1=0, j2=math.radians(-10), j3=0, j4=0, j5=0, j6=0))
   input("Press Enter when position looks good...")
   
   Tool offset correction:
   # Apply tool center point offset
   tool_offset = Pose3D(x=0, y=0, z=0.05, rx=0, ry=0, rz=0)  # 5cm tool length
   robot.movel_relative(tool_offset)
   """)
    
    print("\n5. Safety Notes:")
    print("   ⚠️  Always start with small movements (1-5 degrees, 1-10mm)")
    print("   ⚠️  Check workspace limits before large relative movements")
    print("   ⚠️  Use emergency stop if robot moves unexpectedly")
    print("   ⚠️  Test movements in simulation first when possible")
    
    print("\n✅ Demo completed successfully!")
    print("   To actually move the robot, uncomment the movement commands above.")


def compare_absolute_vs_relative():
    """Compare absolute vs relative movement approaches."""
    print("\n=== Absolute vs Relative Movement Comparison ===")
    
    robot = URRobot()
    current_pose = robot.get_pose()
    
    print("Scenario: Move 5cm forward from current position")
    print(f"Current position: X={current_pose.x:.3f}m")
    
    print("\nAbsolute approach:")
    target_x = current_pose.x + 0.05
    absolute_target = Pose3D(
        x=target_x, y=current_pose.y, z=current_pose.z,
        rx=current_pose.rx, ry=current_pose.ry, rz=current_pose.rz
    )
    print(f"  1. Read current pose: {current_pose}")
    print(f"  2. Calculate target: X={target_x:.3f}m")
    print(f"  3. Execute: robot.movel({absolute_target})")
    
    print("\nRelative approach:")
    relative_delta = Pose3D(x=0.05, y=0, z=0, rx=0, ry=0, rz=0)
    print(f"  1. Define delta: {relative_delta}")
    print(f"  2. Execute: robot.movel_relative({relative_delta})")
    
    print("\nAdvantages of relative movements:")
    print("  ✓ Simpler code - no need to read current state")
    print("  ✓ Less error-prone - no manual coordinate calculation")
    print("  ✓ More intuitive - think in terms of 'move 5cm forward'")
    print("  ✓ Easier to parameterize - delta values can be variables")


def main():
    """Run the demonstration."""
    try:
        demo_relative_movements()
        compare_absolute_vs_relative()
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo failed: {e}")
        print("Make sure the robot is connected and accessible")


if __name__ == "__main__":
    main()
