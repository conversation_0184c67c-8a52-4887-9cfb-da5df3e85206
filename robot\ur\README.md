# UR Robot Implementation

This directory contains the implementation of the Robot protocol for Universal Robots (UR).

## Files

- `ur_robot.py` - Main implementation of the URRobot class
- `realtime/command.py` - Example of sending URScript commands
- `documentation/` - URScript documentation and reference materials

## URRobot Class

The `URRobot` class implements the `Robot` protocol defined in `robot/robot.py` and provides:

### Stateless Design
- Each method call creates its own socket connection
- No persistent connection state to manage
- Simple and robust communication pattern

### Movement Commands
- `movel(pose)` - Linear movement to a target pose
- `movej(joints)` - Joint movement to target joint positions
- `movel_relative(pose_delta)` - Linear movement relative to current pose
- `movej_relative(joints_delta)` - Joint movement relative to current joint positions

### State Reading
- `get_pose()` - Read current TCP pose (x, y, z, rx, ry, rz)
- `get_joints()` - Read current joint positions (j1-j6 in radians)

### Communication Ports
- **Port 30001** (Primary) - For sending URScript commands
- **Port 30002** (Secondary) - For reading robot state data
- **Port 30003** (Realtime) - For real-time data (not used in current implementation)

## Usage Examples

### Basic Usage
```python
from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints

# Create robot instance
robot = URRobot(robot_ip="***************")

# Read current state
joints = robot.get_joints()
pose = robot.get_pose()

# Move robot
target_joints = Joints(0.0, -1.57, -1.57, 0.0, 1.57, 0.0)
robot.movej(target_joints)

# Move to specific pose
target_pose = Pose3D(x=0.3, y=0.0, z=0.4, rx=0.0, ry=3.14159, rz=0.0)
robot.movel(target_pose)

# Relative movements
# Move 5cm forward, 2cm up relative to current position
pose_delta = Pose3D(x=0.05, y=0.0, z=0.02, rx=0.0, ry=0.0, rz=0.0)
robot.movel_relative(pose_delta)

# Move joint 1 by +10 degrees relative to current position
joint_delta = Joints(j1=math.radians(10), j2=0, j3=0, j4=0, j5=0, j6=0)
robot.movej_relative(joint_delta)
```

## Data Formats

### Joint Positions
- All joint angles in radians
- Range: approximately -2π to +2π for each joint
- Order: Base, Shoulder, Elbow, Wrist1, Wrist2, Wrist3

### TCP Pose
- Position: x, y, z in meters (base frame)
- Orientation: rx, ry, rz as axis-angle representation in radians

## Relative Movements

The relative movement methods provide a convenient way to make small adjustments without needing to calculate absolute target positions:

### `movel_relative(pose_delta)`
Moves the robot linearly relative to its current TCP pose. The method:
1. Reads the current TCP pose using `get_pose()`
2. Adds the delta values to the current pose
3. Executes `movel()` to the calculated target pose

**Example use cases:**
- Fine positioning adjustments
- Incremental movements during calibration
- Tool offset corrections

### `movej_relative(joints_delta)`
Moves the robot joints relative to their current positions. The method:
1. Reads the current joint positions using `get_joints()`
2. Adds the delta values to the current joint positions
3. Executes `movej()` to the calculated target joints

**Example use cases:**
- Small joint adjustments
- Teaching positions incrementally
- Avoiding singularities by small joint movements

## Safety Considerations

⚠️ **Important Safety Notes:**

1. **Always test movements carefully** - Start with small movements
2. **Use proper safety setup** - Ensure emergency stops are accessible
3. **Check workspace limits** - Verify target positions are reachable and safe
4. **Monitor robot state** - Check current position before commanding movements
5. **Use appropriate speeds** - Default speeds are conservative but adjust as needed

## Error Handling

The implementation includes comprehensive error handling:

- `ConnectionError` - When unable to connect to robot
- `RuntimeError` - For communication failures or invalid robot state
- Socket timeouts - Prevents hanging on network issues

## Testing

Run the test suite:
```bash
python test_ur_robot.py
```

See usage examples:
```bash
python example_ur_robot_usage.py
```

## Implementation Details

### URScript Command Format
Commands are sent as plain text URScript terminated with `\n\n`:
```
movej([0.0, -1.57, -1.57, 0.0, 1.57, 0.0], a=1.4, v=1.05)\n\n
```

### Robot State Parsing
Robot state is read as binary data with the following structure:
- Message size (4 bytes, big-endian)
- Message type (1 byte)
- Sub-packages containing joint data, cartesian info, etc.

The implementation parses:
- **Joint data package (type 1)** - For joint positions
- **Cartesian info package (type 4)** - For TCP pose

### Default Parameters
- **movej**: acceleration=1.4 rad/s², velocity=1.05 rad/s
- **movel**: acceleration=1.2 m/s², velocity=0.25 m/s
- **Socket timeout**: 5-10 seconds depending on operation

## Dependencies

- `socket` - TCP communication
- `struct` - Binary data parsing
- `typing` - Type hints

No external dependencies required.

## Troubleshooting

### Connection Issues
1. Verify robot IP address
2. Check network connectivity
3. Ensure robot is powered on and in remote control mode
4. Verify firewall settings allow TCP connections

### Movement Issues
1. Check if robot is in proper mode (not in manual mode)
2. Verify target positions are within workspace
3. Ensure no safety stops are active
4. Check for collision detection triggers

### State Reading Issues
1. Verify robot is sending state data
2. Check for network packet loss
3. Ensure robot software version compatibility
