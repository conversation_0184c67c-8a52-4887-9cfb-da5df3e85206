import socket
import struct
from robot.robot import Robot, Pose3D, Joints


class URRobot(Robot):
    """
    Universal Robot implementation of the Robot protocol.

    This class provides control and monitoring capabilities for UR robots
    using socket communication and URScript commands. Each method call
    creates its own socket connection for simplicity and robustness.
    """

    def __init__(self, robot_ip: str = "***************"):
        """
        Initialize the UR robot.

        Args:
            robot_ip: IP address of the UR robot (default: "***************")
        """
        self.robot_ip = robot_ip
        self.primary_port = 30001  # For sending commands
        self.secondary_port = 30002  # For reading state

    def _send_urscript_command(self, command: str) -> None:
        """
        Send a URScript command to the robot.

        Args:
            command: URScript command to send
        """
        try:
            # Create socket connection
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as cmd_socket:
                cmd_socket.settimeout(10.0)  # 10 second timeout
                cmd_socket.connect((self.robot_ip, self.primary_port))

                # URScript commands must end with double newline
                command_with_newline = command + "\n\n"

                # Send the command
                cmd_socket.sendall(command_with_newline.encode("utf-8"))

                # Read response (at least 79 bytes as per documentation)
                _ = cmd_socket.recv(1024)  # Read and discard response

            print(f"Command sent successfully: {command}")

        except Exception as e:
            raise RuntimeError(f"Failed to send command '{command}': {e}")

    def _read_robot_state(self) -> bytes:
        """
        Read robot state data from the secondary port.

        Returns:
            Raw robot state data as bytes
        """
        try:
            # Connect to secondary port for reading state
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as state_socket:
                state_socket.settimeout(5.0)  # 5 second timeout
                state_socket.connect((self.robot_ip, self.secondary_port))

                # Read the message size first (4 bytes, big-endian integer)
                size_data = state_socket.recv(4)
                if len(size_data) != 4:
                    raise RuntimeError("Failed to read message size")

                message_size = struct.unpack(">I", size_data)[0]

                # Read the rest of the message
                remaining_data = state_socket.recv(message_size - 4)

                return size_data + remaining_data

        except Exception as e:
            raise RuntimeError(f"Failed to read robot state: {e}")

    def movel(self, pose: Pose3D) -> None:
        """
        Move the robot to a pose using linear movement.

        Args:
            pose: Target pose (x, y, z, rx, ry, rz)
        """
        # Convert pose to URScript format
        pose_list = [pose.x, pose.y, pose.z, pose.rx, pose.ry, pose.rz]

        # Create URScript movel command with reasonable defaults
        # a=1.2 (acceleration), v=0.25 (velocity)
        command = f"movel(p{pose_list}, a=1.2, v=0.25)"

        self._send_urscript_command(command)

    def movej(self, joints: Joints) -> None:
        """
        Move the robot to joint positions.

        Args:
            joints: Target joint positions in radians
        """
        # Convert joints to URScript format
        joint_list = [joints.j1, joints.j2, joints.j3, joints.j4, joints.j5, joints.j6]

        # Create URScript movej command with reasonable defaults
        # a=1.4 (acceleration), v=1.05 (velocity)
        command = f"movej({joint_list}, a=1.4, v=1.05)"

        self._send_urscript_command(command)

    def _parse_joint_positions(self, data: bytes) -> Joints:
        """
        Parse joint positions from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Current joint positions
        """
        try:
            # Skip to joint data section
            # The joint data starts after the robot mode data
            # Robot mode data is typically at offset 5 (1 byte message type + 4 bytes size)
            # Joint data package starts after robot mode data package

            offset = 5  # Skip message type

            # Find joint data package (type 1)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 1:  # Joint data package
                    # Joint positions start at offset 13 within the joint package
                    joint_offset = offset + 13

                    # Read 6 joint positions (8 bytes each, double precision)
                    joints_data = []
                    for i in range(6):
                        joint_pos = struct.unpack(
                            ">d",
                            data[joint_offset + i * 8 : joint_offset + (i + 1) * 8],
                        )[0]
                        joints_data.append(joint_pos)

                    return Joints(
                        j1=joints_data[0],
                        j2=joints_data[1],
                        j3=joints_data[2],
                        j4=joints_data[3],
                        j5=joints_data[4],
                        j6=joints_data[5],
                    )

                offset += package_size

            raise RuntimeError("Joint data not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse joint positions: {e}")

    def _parse_tcp_pose(self, data: bytes) -> Pose3D:
        """
        Parse TCP pose from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Current TCP pose
        """
        try:
            # Skip to cartesian info section
            offset = 5  # Skip message type

            # Find cartesian info package (type 4)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 4:  # Cartesian info package
                    # TCP pose starts at offset 5 within the cartesian package
                    pose_offset = offset + 5

                    # Read 6 pose values (8 bytes each, double precision)
                    pose_data = []
                    for i in range(6):
                        pose_val = struct.unpack(
                            ">d", data[pose_offset + i * 8 : pose_offset + (i + 1) * 8]
                        )[0]
                        pose_data.append(pose_val)

                    return Pose3D(
                        x=pose_data[0],
                        y=pose_data[1],
                        z=pose_data[2],
                        rx=pose_data[3],
                        ry=pose_data[4],
                        rz=pose_data[5],
                    )

                offset += package_size

            raise RuntimeError("Cartesian info not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse TCP pose: {e}")

    def get_joints(self) -> Joints:
        """
        Get current joint positions.

        Returns:
            Current joint positions in radians
        """
        data = self._read_robot_state()
        return self._parse_joint_positions(data)

    def get_pose(self) -> Pose3D:
        """
        Get current TCP pose.

        Returns:
            Current TCP pose (x, y, z, rx, ry, rz)
        """
        data = self._read_robot_state()
        return self._parse_tcp_pose(data)
