#!/usr/bin/env python3
"""
Example usage of the URRobot implementation.

This script demonstrates how to use the URRobot class to control
a Universal Robot and read its state.
"""

from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints
import time


def example_basic_usage():
    """Basic usage example with explicit connect/disconnect."""
    print("=== Basic Usage Example ===")
    
    # Create robot instance
    robot = URRobot(robot_ip="***************")
    
    try:
        # Connect to robot
        robot.connect()
        print("Connected to robot")
        
        # Read current state
        current_joints = robot.get_joints()
        current_pose = robot.get_pose()
        
        print(f"Current joint positions: {current_joints}")
        print(f"Current TCP pose: {current_pose}")
        
        # Example movement (commented out for safety)
        # Move to a safe joint configuration
        # safe_joints = Joints(
        #     j1=0.0, j2=-1.57, j3=-1.57, 
        #     j4=0.0, j5=1.57, j6=0.0
        # )
        # robot.movej(safe_joints)
        # print("Moved to safe position")
        
    finally:
        # Always disconnect
        robot.disconnect()
        print("Disconnected from robot")


def example_context_manager():
    """Example using context manager (recommended approach)."""
    print("\n=== Context Manager Example ===")
    
    # Using context manager automatically handles connect/disconnect
    with URRobot(robot_ip="***************") as robot:
        print("Connected to robot via context manager")
        
        # Read current state
        joints = robot.get_joints()
        pose = robot.get_pose()
        
        print(f"Joint positions: {joints}")
        print(f"TCP pose: {pose}")
        
        # Example of moving to a specific pose (commented for safety)
        # target_pose = Pose3D(
        #     x=0.3, y=0.0, z=0.4,  # 30cm forward, 40cm up
        #     rx=0.0, ry=3.14159, rz=0.0  # Tool pointing down
        # )
        # robot.movel(target_pose)
        # print("Moved to target pose")
        
    print("Automatically disconnected")


def example_monitoring_loop():
    """Example of continuously monitoring robot state."""
    print("\n=== Monitoring Loop Example ===")
    
    with URRobot() as robot:
        print("Starting monitoring loop (press Ctrl+C to stop)")
        
        try:
            for i in range(10):  # Monitor for 10 iterations
                joints = robot.get_joints()
                pose = robot.get_pose()
                
                print(f"Iteration {i+1}:")
                print(f"  Joints: [{joints.j1:.3f}, {joints.j2:.3f}, {joints.j3:.3f}, "
                      f"{joints.j4:.3f}, {joints.j5:.3f}, {joints.j6:.3f}]")
                print(f"  Pose: [{pose.x:.3f}, {pose.y:.3f}, {pose.z:.3f}, "
                      f"{pose.rx:.3f}, {pose.ry:.3f}, {pose.rz:.3f}]")
                
                time.sleep(1)  # Wait 1 second between readings
                
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")


def example_error_handling():
    """Example of proper error handling."""
    print("\n=== Error Handling Example ===")
    
    try:
        # Try to connect to a robot that might not exist
        robot = URRobot(robot_ip="192.168.1.999")  # Invalid IP
        robot.connect()
        
    except ConnectionError as e:
        print(f"Connection error: {e}")
        print("This is expected if the robot is not available")
    
    except Exception as e:
        print(f"Unexpected error: {e}")


def example_safe_movement_pattern():
    """Example of a safe movement pattern."""
    print("\n=== Safe Movement Pattern Example ===")
    
    with URRobot() as robot:
        # Always read current position first
        start_joints = robot.get_joints()
        start_pose = robot.get_pose()
        
        print(f"Starting position - Joints: {start_joints}")
        print(f"Starting position - Pose: {start_pose}")
        
        # Define safe intermediate positions
        # (These are just examples - adjust for your specific robot setup)
        
        # Example 1: Move joints individually (safer)
        # intermediate_joints = Joints(
        #     j1=start_joints.j1,
        #     j2=start_joints.j2 + 0.1,  # Small movement
        #     j3=start_joints.j3,
        #     j4=start_joints.j4,
        #     j5=start_joints.j5,
        #     j6=start_joints.j6
        # )
        # robot.movej(intermediate_joints)
        # print("Moved to intermediate position")
        
        # Example 2: Move back to start position
        # robot.movej(start_joints)
        # print("Returned to start position")
        
        print("Movement pattern completed (movements commented out for safety)")


if __name__ == "__main__":
    print("URRobot Usage Examples")
    print("=" * 50)
    
    try:
        example_basic_usage()
        example_context_manager()
        example_monitoring_loop()
        example_error_handling()
        example_safe_movement_pattern()
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error in examples: {e}")
    
    print("\n" + "=" * 50)
    print("Examples completed")
    print("\nNote: Movement commands are commented out for safety.")
    print("Uncomment them only when you're ready to move the robot!")
