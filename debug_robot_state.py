#!/usr/bin/env python3
"""
Debug script to test robot state reading and examine raw data.
"""

import socket
import struct
from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints


def debug_raw_robot_state(robot_ip="***************", port=30002):
    """Read and examine raw robot state data."""
    print(f"=== Debugging Raw Robot State (Port {port}) ===")

    try:
        # Connect to specified port for reading state
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as state_socket:
            state_socket.settimeout(10.0)
            print(f"Connecting to {robot_ip}:{port}...")
            state_socket.connect((robot_ip, port))
            print("✓ Connected successfully")

            # Try to read multiple messages to find robot state
            for attempt in range(5):
                print(f"\n--- Message {attempt + 1} ---")

                # Read the message size first (4 bytes, big-endian integer)
                print("Reading message size...")
                size_data = state_socket.recv(4)
                if len(size_data) != 4:
                    print(f"✗ Failed to read message size, got {len(size_data)} bytes")
                    continue

                message_size = struct.unpack(">I", size_data)[0]
                print(f"✓ Message size: {message_size} bytes")

                # Read the rest of the message
                print("Reading message data...")
                remaining_data = state_socket.recv(message_size - 4)
                print(f"✓ Read {len(remaining_data)} bytes of data")

                full_data = size_data + remaining_data
                print(f"✓ Total data length: {len(full_data)} bytes")

                # Examine the message structure
                if len(full_data) >= 5:
                    message_type = struct.unpack(">B", full_data[4:5])[0]
                    print(f"Message type: {message_type}")

                    # Check if this is a robot state message (type 16)
                    if message_type == 16:
                        print("🎯 Found robot state message!")
                        analyze_robot_state_message(full_data)
                        return full_data
                    else:
                        print(f"  → Skipping message type {message_type}")
                        if message_type == 20:
                            print("  → This is a robot message (version/safety/etc.)")

            print("⚠️ No robot state message found in 5 attempts")
            return None

    except Exception as e:
        print(f"✗ Error reading robot state: {e}")
        return None


def analyze_robot_state_message(data):
    """Analyze a robot state message (type 16)."""
    print("\n=== Robot State Message Analysis ===")

    # Look for sub-packages
    offset = 5  # Skip message type
    package_count = 0

    while offset < len(data) - 8:
        try:
            package_size = struct.unpack(">I", data[offset : offset + 4])[0]
            package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

            print(
                f"Package {package_count}: type={package_type}, size={package_size}, offset={offset}"
            )

            # If this is joint data (type 1), examine it
            if package_type == 1:
                print("  → Joint data package found!")
                examine_joint_data(data, offset)

            # If this is cartesian info (type 4), examine it
            elif package_type == 4:
                print("  → Cartesian info package found!")
                examine_cartesian_data(data, offset)

            offset += package_size
            package_count += 1

            if package_count > 20:  # Safety limit
                print("Stopping after 20 packages...")
                break

        except Exception as e:
            print(f"Error parsing package at offset {offset}: {e}")
            break


def try_urscript_approach(robot_ip="***************"):
    """Try using URScript to get robot state."""
    print("\n=== Trying URScript Approach ===")

    try:
        # Send URScript command to get joint positions
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as cmd_socket:
            cmd_socket.settimeout(10.0)
            cmd_socket.connect((robot_ip, 30001))

            # Send URScript to get joint positions
            script = "def get_state():\n  textmsg(get_actual_joint_positions())\n  textmsg(get_actual_tcp_pose())\nend\nget_state()\n\n"
            cmd_socket.sendall(script.encode("utf-8"))

            # Read response
            response = cmd_socket.recv(1024)
            print(f"URScript response: {response}")

    except Exception as e:
        print(f"✗ URScript approach failed: {e}")


def try_realtime_port(robot_ip="***************"):
    """Try the realtime port (30003)."""
    print("\n=== Trying Realtime Port (30003) ===")
    return debug_raw_robot_state(robot_ip, 30003)


def examine_joint_data(data, package_offset):
    """Examine joint data package in detail."""
    print("    Examining joint data...")
    try:
        # Joint positions typically start at offset 13 within the package
        joint_offset = package_offset + 13

        if joint_offset + 48 <= len(data):  # 6 joints * 8 bytes each
            joints = []
            for i in range(6):
                joint_pos = struct.unpack(
                    ">d", data[joint_offset + i * 8 : joint_offset + (i + 1) * 8]
                )[0]
                joints.append(joint_pos)
                print(
                    f"    Joint {i + 1}: {joint_pos:.6f} rad ({joint_pos * 180 / 3.14159:.2f}°)"
                )
        else:
            print("    ✗ Not enough data for joint positions")

    except Exception as e:
        print(f"    ✗ Error parsing joint data: {e}")


def examine_cartesian_data(data, package_offset):
    """Examine cartesian data package in detail."""
    print("    Examining cartesian data...")
    try:
        # TCP pose typically starts at offset 5 within the package
        pose_offset = package_offset + 5

        if pose_offset + 48 <= len(data):  # 6 values * 8 bytes each
            pose = []
            for i in range(6):
                pose_val = struct.unpack(
                    ">d", data[pose_offset + i * 8 : pose_offset + (i + 1) * 8]
                )[0]
                pose.append(pose_val)

            print(f"    X: {pose[0]:.6f} m")
            print(f"    Y: {pose[1]:.6f} m")
            print(f"    Z: {pose[2]:.6f} m")
            print(f"    Rx: {pose[3]:.6f} rad ({pose[3] * 180 / 3.14159:.2f}°)")
            print(f"    Ry: {pose[4]:.6f} rad ({pose[4] * 180 / 3.14159:.2f}°)")
            print(f"    Rz: {pose[5]:.6f} rad ({pose[5] * 180 / 3.14159:.2f}°)")
        else:
            print("    ✗ Not enough data for pose")

    except Exception as e:
        print(f"    ✗ Error parsing cartesian data: {e}")


def test_robot_methods():
    """Test the actual robot methods."""
    print("\n=== Testing Robot Methods ===")

    robot = URRobot()

    print("Testing get_joints()...")
    try:
        joints = robot.get_joints()
        print(f"✓ get_joints() returned: {joints}")
    except Exception as e:
        print(f"✗ get_joints() failed: {e}")

    print("\nTesting get_pose()...")
    try:
        pose = robot.get_pose()
        print(f"✓ get_pose() returned: {pose}")
    except Exception as e:
        print(f"✗ get_pose() failed: {e}")


def main():
    """Run all debug tests."""
    print("Robot State Debugging Tool")
    print("=" * 50)

    # Try different approaches to get robot state

    # 1. Try secondary port (30002) - multiple messages
    raw_data = debug_raw_robot_state()

    # 2. Try realtime port (30003)
    if not raw_data:
        raw_data = try_realtime_port()

    # 3. Try URScript approach
    try_urscript_approach()

    if raw_data:
        print(f"\n=== Raw Data Sample (first 100 bytes) ===")
        for i in range(0, min(100, len(raw_data)), 16):
            hex_part = " ".join(f"{b:02x}" for b in raw_data[i : i + 16])
            ascii_part = "".join(
                chr(b) if 32 <= b <= 126 else "." for b in raw_data[i : i + 16]
            )
            print(f"{i:04x}: {hex_part:<48} {ascii_part}")

    # Then test our methods
    test_robot_methods()

    print("\n" + "=" * 50)
    print("Debug complete")


if __name__ == "__main__":
    main()
