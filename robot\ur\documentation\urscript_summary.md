# Summary of URScript Programming Language Manual (v5.12)

## Table of Contents

- [Summary of URScript Programming Language Manual (v5.12)](#summary-of-urscript-programming-language-manual-v512)
  - [Table of Contents](#table-of-contents)
  - [1. Introduction](#1-introduction)
  - [2. Connecting to URControl](#2-connecting-to-urcontrol)
  - [3. Numbers, Variables, and Types](#3-numbers-variables-and-types)
    - [Types:](#types)
    - [Examples:](#examples)
  - [4. Matrix and Array Expressions](#4-matrix-and-array-expressions)
    - [Examples:](#examples-1)
    - [Supported Operations:](#supported-operations)
  - [5. Flow of Control](#5-flow-of-control)
    - [If-Else:](#if-else)
    - [While Loops:](#while-loops)
    - [Special keywords:](#special-keywords)
  - [6. Function](#6-function)
  - [7. Remote Procedure Call (RPC)](#7-remote-procedure-call-rpc)
  - [8. Scoping Rules](#8-scoping-rules)
  - [9. Threads](#9-threads)
    - [Critical Sections:](#critical-sections)
  - [10. Program Label](#10-program-label)
  - [11. Secondary Programs](#11-secondary-programs)
    - [Limitations:](#limitations)
  - [12. Interpreter Mode](#12-interpreter-mode)
    - [Connect to port `30020`:](#connect-to-port-30020)
    - [Commands:](#commands)
    - [State commands:](#state-commands)
  - [13. Module `motion`](#13-module-motion)
    - [Movement:](#movement)
    - [Servo:](#servo)
    - [Speed:](#speed)
    - [Force Mode:](#force-mode)
    - [Teach \& Freedrive:](#teach--freedrive)
  - [14. Module `internals`](#14-module-internals)
    - [Get State:](#get-state)
    - [Set State:](#set-state)
    - [Utilities:](#utilities)
  - [15. Module `urmath`](#15-module-urmath)
  - [16. Module `interfaces`](#16-module-interfaces)
    - [Modbus:](#modbus)
    - [Sockets:](#sockets)
  - [17. Module `ioconfiguration`](#17-module-ioconfiguration)
  - [18. Module `processpath`](#18-module-processpath)

---

## 1. Introduction

URScript is the scripting language for Universal Robots (e-Series), enabling real-time control at a low level. You can use it for:

* Defining variables and functions
* Controlling I/O
* Real-time motion and feedback

---

## 2. Connecting to URControl

* Connect to **port 30002** via TCP
* Scripts are plaintext, ASCII-encoded, terminated with `\n`
* Must start with `def` or `sec`, and end with `end`

> ⚠️ Always read at least **79 bytes** from the socket before closing it.

---

## 3. Numbers, Variables, and Types

### Types:

* `none`, `bool`, `number` (int/float), `pose`, `string`, `list`

### Examples:

```python
a = 5
b = 3.14
str = "Hello"
pose = p[0.1, 0.2, 0.3, 0, 3.14, 0]
```

Strings are byte arrays, not Unicode-aware. Functions like `str_len` return byte length.

---

## 4. Matrix and Array Expressions

### Examples:

```python
a = [1,2,3]
a[1] = 10

m = [[1,2],[3,4]]
m[1,0] = 9
```

### Supported Operations:

* Element-wise: `+`, `-`, `*`, `/`, `%`
* Scalar: `matrix + 1`
* Matrix-matrix multiplication
* Matrix \* array

---

## 5. Flow of Control

### If-Else:

```python
if a > 3:
  a = a + 1
elif b < 7:
  b = b * a
else:
  a = a + b
end
```

### While Loops:

```python
i = 0
while i < 5:
  i = i + 1
end
```

### Special keywords:

* `halt`: stop program
* `return`: return from function (optionally `return None`)
* `break`: exit loop

---

## 6. Function

```python
def add(a, b):
  return a + b
end

add(3, 4)
```

* Supports **default arguments** and **named parameters**
* All arguments are passed by value (not by reference)

---

## 7. Remote Procedure Call (RPC)

URScript supports XML-RPC:

```python
camera = rpc_factory("xmlrpc", "http://127.0.0.1/RPC2")
camera.initialize("RGB")
camera.takeSnapshot()
target = camera.getTarget()
```

* RPC functions must exist remotely
* UR waits for return values

---

## 8. Scoping Rules

* `global var` makes a variable global
* `local var` restricts variable to function scope
* Unqualified variables default to global if they exist, else local

Example:

```python
def prog():
  global a = 0
  def inner():
    local a = 1
  end
end
```

---

## 9. Threads

Declare threads like functions:

```python
thread myThread():
  # do work
  return False
end

th = run myThread()
join th
kill th
```

### Critical Sections:

```python
enter_critical
# protected code
exit_critical
```

* Only one thread can be in a critical section at a time
* Avoid time-consuming calls inside (e.g., `sleep`, `movej`)

---

## 10. Program Label

Used internally by PolyScope:

```python
$ 2 "var_1 = True"
global var_1 = True
```

---

## 11. Secondary Programs

Use `sec` instead of `def`:

```python
sec mySecondary():
  set_digital_out(1, True)
end
```

### Limitations:

* ❌ No `movej`, `movel`, `sleep`, `thread`
* ✅ Only quick, non-blocking commands
* Slow operations may cause **"runtime too much behind"**

---

## 12. Interpreter Mode

Execute code live during program runtime.

### Connect to port `30020`:

* Send code line-by-line, single-line format:

```python
def myFun(): textmsg("Hi") end
```

### Commands:

* `interpreter_mode(clearQueueOnEnter=True, clearOnEnd=True)`
* `end_interpreter()`
* `clear_interpreter()`
* `abort` — aborts `movej`, `movel`
* `skipbuffer` — skip pending interpreter statements

### State commands:

* `statelastexecuted`
* `statelastinterpreted`
* `statelastcleared`
* `stateunexecuted`

---

## 13. Module `motion`

### Movement:

```python
movej(q, a, v, t=0, r=0)
movel(pose, a, v, t=0, r=0)
movec(pose_via, pose_to)
movep(pose, a, v, r)
```

### Servo:

```python
servoj(q, a, v, t=0.002, lookahead_time=0.1, gain=300)
servoc(pose, a, v, r)
```

### Speed:

```python
speedj(qd, a, t)
speedl(xd, a, t)
stopj(a)
stopl(a)
```

### Force Mode:

```python
force_mode(task_frame, selection_vector, wrench, type, limits)
end_force_mode()
force_mode_set_damping(0.5)
force_mode_set_gain_scaling(1.0)
```

### Teach & Freedrive:

```python
freedrive_mode()
end_freedrive_mode()
teach_mode()
end_teach_mode()
```

---

## 14. Module `internals`

### Get State:

```python
get_actual_joint_positions()
get_actual_tcp_pose()
get_actual_tcp_speed()
get_controller_temp()
get_tool_current()
get_tcp_force()
get_tool_accelerometer_reading()
```

### Set State:

```python
set_payload(mass, cog)
set_tcp(pose)
set_gravity([0, 0, 9.82])
set_payload_mass(2.0)
```

### Utilities:

```python
textmsg("Hello")
to_str(42)
to_num("3.14")
popup("Warning!")
sleep(0.5)
```

---

## 15. Module `urmath`

Math helpers:

```python
acos(x), asin(x), atan2(y, x)
d2r(deg), r2d(rad)
cos(x), sin(x), tan(x)
floor(x), ceil(x), round(x)
length(vec), norm(vec), normalize(vec)
pose_add(p1, p2), pose_sub(p1, p2)
pose_trans(p1, p2), pose_inv(p)
interpolate_pose(p1, p2, alpha)
```

---

## 16. Module `interfaces`

I/O functions:

```python
get_digital_in(n), get_digital_out(n)
set_digital_out(n, True/False)
get_analog_in(n), set_analog_out(n, f)
get_flag(n), set_flag(n, True)
```

### Modbus:

```python
modbus_add_signal(...)
modbus_set_output_signal(...)
```

### Sockets:

```python
socket_open("192.168.0.1", 3000)
socket_send_string("start")
socket_read_line()
socket_close()
```

---

## 17. Module `ioconfiguration`

Configure IO behavior per run-state:

```python
set_input_actions_to_default()
set_tool_digital_input_action(port, action)
set_standard_digital_input_action(port, action)
set_runstate_standard_digital_output_to_value(port, True)
```

---

## 18. Module `processpath`

Remote TCP and path control API:

```python
mc_initialize(mode, tcp)
mc_load_path("myFile.nc", useFeedRate=True)
mc_add_linear(pose, a, v, r)
mc_add_circular(pose_via, pose_to, a, v, r)
mc_run_motion()
```

