#!/usr/bin/env python3
"""
Test script for relative movement methods.

This script tests the movel_relative and movej_relative methods
without actually moving the robot (for safety).
"""

from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints
import math


def test_relative_movement_calculations():
    """Test the calculation logic of relative movements without moving."""
    print("=== Testing Relative Movement Calculations ===")
    
    robot = URRobot()
    
    # Get current state
    print("Reading current robot state...")
    current_joints = robot.get_joints()
    current_pose = robot.get_pose()
    
    print(f"Current joints (degrees): [{current_joints.j1*180/math.pi:.1f}, "
          f"{current_joints.j2*180/math.pi:.1f}, {current_joints.j3*180/math.pi:.1f}, "
          f"{current_joints.j4*180/math.pi:.1f}, {current_joints.j5*180/math.pi:.1f}, "
          f"{current_joints.j6*180/math.pi:.1f}]")
    
    print(f"Current pose: X={current_pose.x:.3f}, Y={current_pose.y:.3f}, Z={current_pose.z:.3f}, "
          f"Rx={current_pose.rx*180/math.pi:.1f}°, Ry={current_pose.ry*180/math.pi:.1f}°, "
          f"Rz={current_pose.rz*180/math.pi:.1f}°")
    
    # Test joint relative calculation
    print("\n--- Testing Joint Relative Calculation ---")
    joint_delta = Joints(
        j1=math.radians(10),   # +10 degrees
        j2=math.radians(-5),   # -5 degrees
        j3=0.0,                # no change
        j4=math.radians(15),   # +15 degrees
        j5=0.0,                # no change
        j6=math.radians(-10)   # -10 degrees
    )
    
    print(f"Joint delta (degrees): [{joint_delta.j1*180/math.pi:.1f}, "
          f"{joint_delta.j2*180/math.pi:.1f}, {joint_delta.j3*180/math.pi:.1f}, "
          f"{joint_delta.j4*180/math.pi:.1f}, {joint_delta.j5*180/math.pi:.1f}, "
          f"{joint_delta.j6*180/math.pi:.1f}]")
    
    # Calculate expected result manually
    expected_joints = Joints(
        j1=current_joints.j1 + joint_delta.j1,
        j2=current_joints.j2 + joint_delta.j2,
        j3=current_joints.j3 + joint_delta.j3,
        j4=current_joints.j4 + joint_delta.j4,
        j5=current_joints.j5 + joint_delta.j5,
        j6=current_joints.j6 + joint_delta.j6
    )
    
    print(f"Expected target joints (degrees): [{expected_joints.j1*180/math.pi:.1f}, "
          f"{expected_joints.j2*180/math.pi:.1f}, {expected_joints.j3*180/math.pi:.1f}, "
          f"{expected_joints.j4*180/math.pi:.1f}, {expected_joints.j5*180/math.pi:.1f}, "
          f"{expected_joints.j6*180/math.pi:.1f}]")
    
    # Test pose relative calculation
    print("\n--- Testing Pose Relative Calculation ---")
    pose_delta = Pose3D(
        x=0.05,                # +5cm in X
        y=-0.02,               # -2cm in Y
        z=0.1,                 # +10cm in Z
        rx=math.radians(5),    # +5 degrees rotation around X
        ry=0.0,                # no change
        rz=math.radians(-10)   # -10 degrees rotation around Z
    )
    
    print(f"Pose delta: X={pose_delta.x:.3f}m, Y={pose_delta.y:.3f}m, Z={pose_delta.z:.3f}m, "
          f"Rx={pose_delta.rx*180/math.pi:.1f}°, Ry={pose_delta.ry*180/math.pi:.1f}°, "
          f"Rz={pose_delta.rz*180/math.pi:.1f}°")
    
    # Calculate expected result manually
    expected_pose = Pose3D(
        x=current_pose.x + pose_delta.x,
        y=current_pose.y + pose_delta.y,
        z=current_pose.z + pose_delta.z,
        rx=current_pose.rx + pose_delta.rx,
        ry=current_pose.ry + pose_delta.ry,
        rz=current_pose.rz + pose_delta.rz
    )
    
    print(f"Expected target pose: X={expected_pose.x:.3f}m, Y={expected_pose.y:.3f}m, Z={expected_pose.z:.3f}m, "
          f"Rx={expected_pose.rx*180/math.pi:.1f}°, Ry={expected_pose.ry*180/math.pi:.1f}°, "
          f"Rz={expected_pose.rz*180/math.pi:.1f}°")
    
    print("\n✓ Calculation tests completed successfully!")
    return True


def test_relative_movement_methods():
    """Test the relative movement methods (without actually moving)."""
    print("\n=== Testing Relative Movement Methods ===")
    
    robot = URRobot()
    
    # Test small relative movements (commented out for safety)
    print("Testing movej_relative method structure...")
    small_joint_delta = Joints(
        j1=math.radians(1),    # +1 degree
        j2=0.0, j3=0.0, j4=0.0, j5=0.0, j6=0.0
    )
    
    print(f"Small joint delta: J1={small_joint_delta.j1*180/math.pi:.1f}° (others=0°)")
    print("⚠️  movej_relative call commented out for safety")
    # robot.movej_relative(small_joint_delta)  # Uncomment to actually move
    print("✓ movej_relative method structure valid")
    
    print("\nTesting movel_relative method structure...")
    small_pose_delta = Pose3D(
        x=0.01,  # +1cm in X
        y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0
    )
    
    print(f"Small pose delta: X={small_pose_delta.x:.3f}m (others=0)")
    print("⚠️  movel_relative call commented out for safety")
    # robot.movel_relative(small_pose_delta)  # Uncomment to actually move
    print("✓ movel_relative method structure valid")
    
    return True


def test_protocol_compliance():
    """Test that URRobot implements all Robot protocol methods."""
    print("\n=== Testing Protocol Compliance ===")
    
    from robot.robot import Robot
    import inspect
    
    # Check if URRobot implements all Robot protocol methods
    robot_methods = [method for method in dir(Robot) if not method.startswith('_')]
    ur_robot_methods = [method for method in dir(URRobot) if not method.startswith('_')]
    
    print(f"Robot protocol methods: {robot_methods}")
    print(f"URRobot implemented methods: {[m for m in ur_robot_methods if m in robot_methods]}")
    
    # Check method signatures
    missing_methods = []
    for method_name in robot_methods:
        if hasattr(URRobot, method_name):
            robot_sig = inspect.signature(getattr(Robot, method_name))
            ur_robot_sig = inspect.signature(getattr(URRobot, method_name))
            print(f"✓ {method_name}: {robot_sig} -> {ur_robot_sig}")
        else:
            missing_methods.append(method_name)
            print(f"✗ Missing method: {method_name}")
    
    if missing_methods:
        print(f"❌ Missing methods: {missing_methods}")
        return False
    else:
        print("✅ All protocol methods implemented!")
        return True


def demonstrate_relative_movements():
    """Demonstrate how to use relative movements safely."""
    print("\n=== Relative Movement Usage Examples ===")
    
    print("""
# Example 1: Small joint adjustments
robot = URRobot()

# Move joint 1 by +5 degrees, keep others unchanged
joint_delta = Joints(j1=math.radians(5), j2=0, j3=0, j4=0, j5=0, j6=0)
robot.movej_relative(joint_delta)

# Example 2: Small cartesian movements  
# Move 2cm forward (X), 1cm up (Z)
pose_delta = Pose3D(x=0.02, y=0.0, z=0.01, rx=0, ry=0, rz=0)
robot.movel_relative(pose_delta)

# Example 3: Pure rotation
# Rotate tool 10 degrees around Z-axis
rotation_delta = Pose3D(x=0, y=0, z=0, rx=0, ry=0, rz=math.radians(10))
robot.movel_relative(rotation_delta)

# Example 4: Combined movement
# Move and rotate simultaneously
combined_delta = Pose3D(
    x=0.05, y=-0.02, z=0.03,           # 5cm forward, 2cm left, 3cm up
    rx=0, ry=0, rz=math.radians(-15)   # rotate -15 degrees around Z
)
robot.movel_relative(combined_delta)
""")
    
    print("✓ Usage examples provided")


def main():
    """Run all tests."""
    print("Relative Movement Methods Test Suite")
    print("=" * 50)
    
    tests = [
        test_relative_movement_calculations,
        test_relative_movement_methods,
        test_protocol_compliance,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
            break
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with unexpected error: {e}")
    
    # Show usage examples
    demonstrate_relative_movements()
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
