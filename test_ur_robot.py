#!/usr/bin/env python3
"""
Test script for URRobot implementation.

This script tests the URRobot class to ensure it properly implements
the Robot protocol and can communicate with a UR robot.
"""

import sys
from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints


def test_instantiation():
    """Test basic robot instantiation."""
    print("Testing robot instantiation...")

    try:
        robot = URRobot()
        print(f"✓ Robot created with IP: {robot.robot_ip}")
        print(f"✓ Primary port: {robot.primary_port}")
        print(f"✓ Secondary port: {robot.secondary_port}")
        return True
    except Exception as e:
        print(f"✗ Instantiation failed: {e}")
        return False


def test_state_reading():
    """Test reading robot state (requires actual robot connection)."""
    print("\nTesting state reading...")

    try:
        robot = URRobot()

        # Test reading current state
        joints = robot.get_joints()
        pose = robot.get_pose()

        print(f"✓ Current joints: {joints}")
        print(f"✓ Current pose: {pose}")

        # Verify joint data types and ranges
        joint_values = [
            joints.j1,
            joints.j2,
            joints.j3,
            joints.j4,
            joints.j5,
            joints.j6,
        ]
        for i, joint_val in enumerate(joint_values):
            if not isinstance(joint_val, float):
                raise ValueError(f"Joint {i + 1} is not a float: {type(joint_val)}")
            if not (-2 * 3.14159 <= joint_val <= 2 * 3.14159):
                print(
                    f"Warning: Joint {i + 1} value {joint_val} seems out of normal range"
                )

        # Verify pose data types
        pose_values = [pose.x, pose.y, pose.z, pose.rx, pose.ry, pose.rz]
        for i, pose_val in enumerate(pose_values):
            if not isinstance(pose_val, float):
                raise ValueError(f"Pose value {i} is not a float: {type(pose_val)}")

        return True
    except Exception as e:
        print(f"✗ State reading test failed: {e}")
        print("  (This is expected if no robot is connected)")
        return False


def test_movement_commands():
    """Test movement commands (without actually moving)."""
    print("\nTesting movement commands...")

    try:
        robot = URRobot()
            # Test joint movement
            test_joints = Joints(j1=0.0, j2=-1.57, j3=-1.57, j4=0.0, j5=1.57, j6=0.0)

            print("Testing movej command...")
            # Note: This will actually move the robot if connected!
            # Uncomment the line below only if you want to test actual movement
            # robot.movej(test_joints)
            print("✓ movej command structure valid")

            # Test linear movement
            test_pose = Pose3D(x=0.3, y=0.0, z=0.3, rx=0.0, ry=3.14159, rz=0.0)

            print("Testing movel command...")
            # Note: This will actually move the robot if connected!
            # Uncomment the line below only if you want to test actual movement
            # robot.movel(test_pose)
            print("✓ movel command structure valid")

        return True
    except Exception as e:
        print(f"✗ Movement command test failed: {e}")
        return False





def main():
    """Run all tests."""
    print("URRobot Implementation Test Suite")
    print("=" * 40)

    tests = [
        test_instantiation,
        test_state_reading,
        test_movement_commands,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
            break
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with unexpected error: {e}")

    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
