# robot protocol

from typing import Protocol
from dataclasses import dataclass


@dataclass
class Pose3D:
    x: float
    y: float
    z: float
    rx: float
    ry: float
    rz: float


@dataclass
class Joints:
    j1: float
    j2: float
    j3: float
    j4: float
    j5: float
    j6: float


class Robot(Protocol):
    def movel(self, pose: Pose3D) -> None: ...

    def movej(self, joints: Joints) -> None: ...

    def get_pose(self) -> Pose3D: ...

    def get_joints(self) -> Joints: ...
